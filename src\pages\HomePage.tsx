
import React from "react";
import { Navigate } from "react-router-dom";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Scissors,
  VideoIcon,
  BookOpen,
  BarChart,
  Calendar,
  Settings,
  Image,
  Share2,
  Cloud,
  TrendingUp,
  Users,
  Play
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";

const HomePage = () => {
  const { isAuthenticated, user, isLoading } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>; 
  }

  if (!isAuthenticated) {
    return <Navigate to="/landing" />;
  }

  const featuredTools = [
    {
      title: "Smart Clipper",
      description: "Extract the most interesting segments from your videos",
      icon: Scissors,
      link: "/smart-clipper",
      badge: "Popular"
    }
  ];

  // Mock data for latest clips - in real app, fetch from API
  const latestClips = [
    {
      id: "1",
      title: "Product Demo Highlight",
      thumbnail: "https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?q=80&w=300&h=200&auto=format&fit=crop",
      duration: "45s",
      vitalityScore: 85,
      url: "#"
    },
    {
      id: "2",
      title: "Tutorial Best Moment",
      thumbnail: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=300&h=200&auto=format&fit=crop",
      duration: "32s",
      vitalityScore: 92,
      url: "#"
    },
    {
      id: "3",
      title: "Interview Highlight",
      thumbnail: "https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?q=80&w=300&h=200&auto=format&fit=crop",
      duration: "28s",
      vitalityScore: 78,
      url: "#"
    }
  ];

  // Connected social platforms
  const connectedPlatforms = [
    {
      name: "YouTube",
      icon: "🎥",
      connected: false,
      profileUrl: "#",
      followers: "0"
    },
    {
      name: "Instagram",
      icon: "📸",
      connected: false,
      profileUrl: "#",
      followers: "0"
    },
    {
      name: "TikTok",
      icon: "🎵",
      connected: false,
      profileUrl: "#",
      followers: "0"
    },
    {
      name: "Twitter",
      icon: "🐦",
      connected: false,
      profileUrl: "#",
      followers: "0"
    }
  ];

  const quickActions = [
    {
      title: "MyClips",
      description: "View your generated clips",
      icon: VideoIcon,
      link: "/clip-results"
    },
    {
      title: "Analytics",
      description: "View your performance metrics",
      icon: BarChart,
      link: "/analytics"
    },
    {
      title: "Calendar",
      description: "Schedule your content",
      icon: Calendar,
      link: "/calendar"
    },
    {
      title: "Social Integration",
      description: "Connect your social accounts",
      icon: Share2,
      link: "/social-integration"
    }
  ];

  const stats = [
    {
      title: "Videos Created",
      value: "0",
      icon: VideoIcon,
      color: "text-blue-500"
    },
    {
      title: "Total Views",
      value: "0",
      icon: Play,
      color: "text-green-500"
    },
    {
      title: "Credits Available",
      value: user?.credits || 0,
      icon: TrendingUp,
      color: "text-purple-500"
    }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Welcome back, {user?.username || 'User'}!</h1>
            <p className="text-muted-foreground mt-1">
              Your all-in-one AI video creation platform
            </p>
          </div>
          <Link to="/smart-clipper">
            <Button size="lg" className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              Create New Video
            </Button>
          </Link>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <div className={`h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Latest Clips */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">Your Latest Clips</h2>
            <Link to="/clip-results">
              <Button variant="outline" size="sm">
                View All Clips
              </Button>
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {latestClips.map((clip) => (
              <Card key={clip.id} className="group hover:border-primary/50 transition-colors relative overflow-hidden">
                <div className="relative">
                  <img
                    src={clip.thumbnail}
                    alt={clip.title}
                    className="w-full h-48 object-cover"
                  />
                  {/* Hover to play overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                    <Button
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Play
                    </Button>
                  </div>
                  {/* Virality badge */}
                  <div className="absolute top-2 right-2">
                    <Badge
                      className={`text-xs ${
                        clip.vitalityScore > 80
                          ? "bg-green-500 text-white"
                          : clip.vitalityScore > 50
                          ? "bg-yellow-400 text-black"
                          : "bg-red-500 text-white"
                      }`}
                    >
                      {clip.vitalityScore}% viral
                    </Badge>
                  </div>
                  {/* Duration */}
                  <div className="absolute bottom-2 right-2">
                    <Badge variant="outline" className="text-xs bg-black/50 text-white">
                      {clip.duration}
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-medium line-clamp-2">{clip.title}</h3>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Connected Social Platforms */}
        <div>
          <h2 className="text-2xl font-bold mb-4">Connected Platforms</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {connectedPlatforms.map((platform) => (
              <Card key={platform.name} className="hover:border-primary/50 transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{platform.icon}</span>
                      <div>
                        <h3 className="font-medium">{platform.name}</h3>
                        <p className="text-xs text-muted-foreground">
                          {platform.connected ? `${platform.followers} followers` : "Not connected"}
                        </p>
                      </div>
                    </div>
                  </div>
                  {platform.connected ? (
                    <Button variant="outline" size="sm" className="w-full">
                      View Profile
                    </Button>
                  ) : (
                    <Link to="/social-integration">
                      <Button size="sm" className="w-full">
                        Connect
                      </Button>
                    </Link>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-2xl font-bold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Link key={action.title} to={action.link}>
                <Card className="hover:border-primary/50 transition-colors cursor-pointer">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                        <action.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium">{action.title}</h3>
                        <p className="text-xs text-muted-foreground">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle>Getting Started with SmartClips</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              Welcome to SmartClips! Here's how to get the most out of our AI-powered video platform:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-blue-500 text-white text-xs flex items-center justify-center font-bold">1</div>
                  <h4 className="font-medium">Upload & Clip</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Use Smart Clipper to automatically extract engaging segments from your videos
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-purple-500 text-white text-xs flex items-center justify-center font-bold">2</div>
                  <h4 className="font-medium">Create Avatars</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Generate AI-powered avatar videos with custom scripts and visuals
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center font-bold">3</div>
                  <h4 className="font-medium">Share & Analyze</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Share your content and track performance with detailed analytics
                </p>
              </div>
            </div>
            <div className="flex gap-2 pt-4">
              <Link to="/smart-clipper">
                <Button>Get Started</Button>
              </Link>
              <Link to="/analytics">
                <Button variant="outline">View Analytics</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default HomePage;
